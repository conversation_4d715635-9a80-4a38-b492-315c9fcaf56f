import {Request} from '../lib/request';
import {Response} from '../lib/response';
import {AWSError} from '../lib/error';
import {Service} from '../lib/service';
import {ServiceConfigurationOptions} from '../lib/service';
import {ConfigBase as Config} from '../lib/config-base';
interface Blob {}
declare class CodeGuruSecurity extends Service {
  /**
   * Constructs a service object. This object has one method for each API operation.
   */
  constructor(options?: CodeGuruSecurity.Types.ClientConfiguration)
  config: Config & CodeGuruSecurity.Types.ClientConfiguration;
  /**
   * Returns a list of requested findings from standard scans.
   */
  batchGetFindings(params: CodeGuruSecurity.Types.BatchGetFindingsRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.BatchGetFindingsResponse) => void): Request<CodeGuruSecurity.Types.BatchGetFindingsResponse, AWSError>;
  /**
   * Returns a list of requested findings from standard scans.
   */
  batchGetFindings(callback?: (err: AWSError, data: CodeGuruSecurity.Types.BatchGetFindingsResponse) => void): Request<CodeGuruSecurity.Types.BatchGetFindingsResponse, AWSError>;
  /**
   * Use to create a scan using code uploaded to an Amazon S3 bucket.
   */
  createScan(params: CodeGuruSecurity.Types.CreateScanRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.CreateScanResponse) => void): Request<CodeGuruSecurity.Types.CreateScanResponse, AWSError>;
  /**
   * Use to create a scan using code uploaded to an Amazon S3 bucket.
   */
  createScan(callback?: (err: AWSError, data: CodeGuruSecurity.Types.CreateScanResponse) => void): Request<CodeGuruSecurity.Types.CreateScanResponse, AWSError>;
  /**
   * Generates a pre-signed URL, request headers used to upload a code resource, and code artifact identifier for the uploaded resource. You can upload your code resource to the URL with the request headers using any HTTP client.
   */
  createUploadUrl(params: CodeGuruSecurity.Types.CreateUploadUrlRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.CreateUploadUrlResponse) => void): Request<CodeGuruSecurity.Types.CreateUploadUrlResponse, AWSError>;
  /**
   * Generates a pre-signed URL, request headers used to upload a code resource, and code artifact identifier for the uploaded resource. You can upload your code resource to the URL with the request headers using any HTTP client.
   */
  createUploadUrl(callback?: (err: AWSError, data: CodeGuruSecurity.Types.CreateUploadUrlResponse) => void): Request<CodeGuruSecurity.Types.CreateUploadUrlResponse, AWSError>;
  /**
   * Use to get the encryption configuration for an account.
   */
  getAccountConfiguration(params: CodeGuruSecurity.Types.GetAccountConfigurationRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.GetAccountConfigurationResponse) => void): Request<CodeGuruSecurity.Types.GetAccountConfigurationResponse, AWSError>;
  /**
   * Use to get the encryption configuration for an account.
   */
  getAccountConfiguration(callback?: (err: AWSError, data: CodeGuruSecurity.Types.GetAccountConfigurationResponse) => void): Request<CodeGuruSecurity.Types.GetAccountConfigurationResponse, AWSError>;
  /**
   * Returns a list of all findings generated by a particular scan.
   */
  getFindings(params: CodeGuruSecurity.Types.GetFindingsRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.GetFindingsResponse) => void): Request<CodeGuruSecurity.Types.GetFindingsResponse, AWSError>;
  /**
   * Returns a list of all findings generated by a particular scan.
   */
  getFindings(callback?: (err: AWSError, data: CodeGuruSecurity.Types.GetFindingsResponse) => void): Request<CodeGuruSecurity.Types.GetFindingsResponse, AWSError>;
  /**
   * Returns a summary of metrics for an account from a specified date, including number of open findings, the categories with most findings, the scans with most open findings, and scans with most open critical findings. 
   */
  getMetricsSummary(params: CodeGuruSecurity.Types.GetMetricsSummaryRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.GetMetricsSummaryResponse) => void): Request<CodeGuruSecurity.Types.GetMetricsSummaryResponse, AWSError>;
  /**
   * Returns a summary of metrics for an account from a specified date, including number of open findings, the categories with most findings, the scans with most open findings, and scans with most open critical findings. 
   */
  getMetricsSummary(callback?: (err: AWSError, data: CodeGuruSecurity.Types.GetMetricsSummaryResponse) => void): Request<CodeGuruSecurity.Types.GetMetricsSummaryResponse, AWSError>;
  /**
   * Returns details about a scan, including whether or not a scan has completed.
   */
  getScan(params: CodeGuruSecurity.Types.GetScanRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.GetScanResponse) => void): Request<CodeGuruSecurity.Types.GetScanResponse, AWSError>;
  /**
   * Returns details about a scan, including whether or not a scan has completed.
   */
  getScan(callback?: (err: AWSError, data: CodeGuruSecurity.Types.GetScanResponse) => void): Request<CodeGuruSecurity.Types.GetScanResponse, AWSError>;
  /**
   * Returns metrics about all findings in an account within a specified time range.
   */
  listFindingsMetrics(params: CodeGuruSecurity.Types.ListFindingsMetricsRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.ListFindingsMetricsResponse) => void): Request<CodeGuruSecurity.Types.ListFindingsMetricsResponse, AWSError>;
  /**
   * Returns metrics about all findings in an account within a specified time range.
   */
  listFindingsMetrics(callback?: (err: AWSError, data: CodeGuruSecurity.Types.ListFindingsMetricsResponse) => void): Request<CodeGuruSecurity.Types.ListFindingsMetricsResponse, AWSError>;
  /**
   * Returns a list of all scans in an account. Does not return EXPRESS scans.
   */
  listScans(params: CodeGuruSecurity.Types.ListScansRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.ListScansResponse) => void): Request<CodeGuruSecurity.Types.ListScansResponse, AWSError>;
  /**
   * Returns a list of all scans in an account. Does not return EXPRESS scans.
   */
  listScans(callback?: (err: AWSError, data: CodeGuruSecurity.Types.ListScansResponse) => void): Request<CodeGuruSecurity.Types.ListScansResponse, AWSError>;
  /**
   * Returns a list of all tags associated with a scan.
   */
  listTagsForResource(params: CodeGuruSecurity.Types.ListTagsForResourceRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.ListTagsForResourceResponse) => void): Request<CodeGuruSecurity.Types.ListTagsForResourceResponse, AWSError>;
  /**
   * Returns a list of all tags associated with a scan.
   */
  listTagsForResource(callback?: (err: AWSError, data: CodeGuruSecurity.Types.ListTagsForResourceResponse) => void): Request<CodeGuruSecurity.Types.ListTagsForResourceResponse, AWSError>;
  /**
   * Use to add one or more tags to an existing scan.
   */
  tagResource(params: CodeGuruSecurity.Types.TagResourceRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.TagResourceResponse) => void): Request<CodeGuruSecurity.Types.TagResourceResponse, AWSError>;
  /**
   * Use to add one or more tags to an existing scan.
   */
  tagResource(callback?: (err: AWSError, data: CodeGuruSecurity.Types.TagResourceResponse) => void): Request<CodeGuruSecurity.Types.TagResourceResponse, AWSError>;
  /**
   * Use to remove one or more tags from an existing scan.
   */
  untagResource(params: CodeGuruSecurity.Types.UntagResourceRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.UntagResourceResponse) => void): Request<CodeGuruSecurity.Types.UntagResourceResponse, AWSError>;
  /**
   * Use to remove one or more tags from an existing scan.
   */
  untagResource(callback?: (err: AWSError, data: CodeGuruSecurity.Types.UntagResourceResponse) => void): Request<CodeGuruSecurity.Types.UntagResourceResponse, AWSError>;
  /**
   * Use to update the encryption configuration for an account.
   */
  updateAccountConfiguration(params: CodeGuruSecurity.Types.UpdateAccountConfigurationRequest, callback?: (err: AWSError, data: CodeGuruSecurity.Types.UpdateAccountConfigurationResponse) => void): Request<CodeGuruSecurity.Types.UpdateAccountConfigurationResponse, AWSError>;
  /**
   * Use to update the encryption configuration for an account.
   */
  updateAccountConfiguration(callback?: (err: AWSError, data: CodeGuruSecurity.Types.UpdateAccountConfigurationResponse) => void): Request<CodeGuruSecurity.Types.UpdateAccountConfigurationResponse, AWSError>;
}
declare namespace CodeGuruSecurity {
  export interface AccountFindingsMetric {
    /**
     * The number of closed findings of each severity on the specified date.
     */
    closedFindings?: FindingMetricsValuePerSeverity;
    /**
     * The date from which the findings metrics were retrieved.
     */
    date?: Timestamp;
    /**
     * The average time in days it takes to close findings of each severity as of a specified date.
     */
    meanTimeToClose?: FindingMetricsValuePerSeverity;
    /**
     * The number of new findings of each severity on the specified date.
     */
    newFindings?: FindingMetricsValuePerSeverity;
    /**
     * The number of open findings of each severity as of the specified date.
     */
    openFindings?: FindingMetricsValuePerSeverity;
  }
  export type AnalysisType = "Security"|"All"|string;
  export interface BatchGetFindingsError {
    /**
     * A code associated with the type of error.
     */
    errorCode: ErrorCode;
    /**
     * The finding ID of the finding that was not fetched.
     */
    findingId: String;
    /**
     * Describes the error.
     */
    message: String;
    /**
     * The name of the scan that generated the finding.
     */
    scanName: ScanName;
  }
  export type BatchGetFindingsErrors = BatchGetFindingsError[];
  export interface BatchGetFindingsRequest {
    /**
     * A list of finding identifiers. Each identifier consists of a scanName and a findingId. You retrieve the findingId when you call GetFindings.
     */
    findingIdentifiers: FindingIdentifiers;
  }
  export interface BatchGetFindingsResponse {
    /**
     * A list of errors for individual findings which were not fetched. Each BatchGetFindingsError contains the scanName, findingId, errorCode and error message.
     */
    failedFindings: BatchGetFindingsErrors;
    /**
     *  A list of all findings which were successfully fetched.
     */
    findings: Findings;
  }
  export type CategoriesWithMostFindings = CategoryWithFindingNum[];
  export interface CategoryWithFindingNum {
    /**
     * The name of the finding category. A finding category is determined by the detector that detected the finding.
     */
    categoryName?: String;
    /**
     * The number of open findings in the category.
     */
    findingNumber?: Integer;
  }
  export type ClientToken = string;
  export interface CodeLine {
    /**
     * The code that contains a vulnerability.
     */
    content?: String;
    /**
     * The code line number.
     */
    number?: Integer;
  }
  export type CodeSnippet = CodeLine[];
  export interface CreateScanRequest {
    /**
     * The type of analysis you want CodeGuru Security to perform in the scan, either Security or All. The Security type only generates findings related to security. The All type generates both security findings and quality findings. Defaults to Security type if missing.
     */
    analysisType?: AnalysisType;
    /**
     * The idempotency token for the request. Amazon CodeGuru Security uses this value to prevent the accidental creation of duplicate scans if there are failures and retries.
     */
    clientToken?: ClientToken;
    /**
     * The identifier for the resource object to be scanned.
     */
    resourceId: ResourceId;
    /**
     * The unique name that CodeGuru Security uses to track revisions across multiple scans of the same resource. Only allowed for a STANDARD scan type. 
     */
    scanName: ScanName;
    /**
     * The type of scan, either Standard or Express. Defaults to Standard type if missing.  Express scans run on limited resources and use a limited set of detectors to analyze your code in near-real time. Standard scans have standard resource limits and use the full set of detectors to analyze your code.
     */
    scanType?: ScanType;
    /**
     * An array of key-value pairs used to tag a scan. A tag is a custom attribute label with two parts:   A tag key. For example, CostCenter, Environment, or Secret. Tag keys are case sensitive.   An optional tag value field. For example, ************, Production, or a team name. Omitting the tag value is the same as using an empty string. Tag values are case sensitive.  
     */
    tags?: TagMap;
  }
  export interface CreateScanResponse {
    /**
     * The identifier for the resource object that contains resources that were scanned.
     */
    resourceId: ResourceId;
    /**
     * UUID that identifies the individual scan run.
     */
    runId: Uuid;
    /**
     * The name of the scan.
     */
    scanName: ScanName;
    /**
     * The ARN for the scan name.
     */
    scanNameArn?: ScanNameArn;
    /**
     * The current state of the scan. Returns either InProgress, Successful, or Failed.
     */
    scanState: ScanState;
  }
  export interface CreateUploadUrlRequest {
    /**
     * The name of the scan that will use the uploaded resource. CodeGuru Security uses the unique scan name to track revisions across multiple scans of the same resource. Use this scanName when you call CreateScan on the code resource you upload to this URL.
     */
    scanName: ScanName;
  }
  export interface CreateUploadUrlResponse {
    /**
     * The identifier for the uploaded code resource. Pass this to CreateScan to use the uploaded resources.
     */
    codeArtifactId: Uuid;
    /**
     * A set of key-value pairs that contain the required headers when uploading your resource.
     */
    requestHeaders: RequestHeaderMap;
    /**
     * A pre-signed S3 URL. You can upload the code file you want to scan with the required requestHeaders using any HTTP client.
     */
    s3Url: S3Url;
  }
  export type DetectorTags = String[];
  export type Double = number;
  export interface EncryptionConfig {
    /**
     * The KMS key ARN that is used for encryption. If an AWS-managed key is used for encryption, returns empty.
     */
    kmsKeyArn?: KmsKeyArn;
  }
  export type ErrorCode = "DUPLICATE_IDENTIFIER"|"ITEM_DOES_NOT_EXIST"|"INTERNAL_ERROR"|"INVALID_FINDING_ID"|"INVALID_SCAN_NAME"|string;
  export type ErrorMessage = string;
  export interface FilePath {
    /**
     * A list of CodeLine objects that describe where the security vulnerability appears in your code.
     */
    codeSnippet?: CodeSnippet;
    /**
     * The last line number of the code snippet where the security vulnerability appears in your code.
     */
    endLine?: Integer;
    /**
     * The name of the file.
     */
    name?: String;
    /**
     * The path to the resource with the security vulnerability.
     */
    path?: String;
    /**
     * The first line number of the code snippet where the security vulnerability appears in your code.
     */
    startLine?: Integer;
  }
  export interface Finding {
    /**
     * The time when the finding was created.
     */
    createdAt?: Timestamp;
    /**
     * A description of the finding.
     */
    description?: String;
    /**
     * The identifier for the detector that detected the finding in your code. A detector is a defined rule based on industry standards and AWS best practices. 
     */
    detectorId?: String;
    /**
     * The name of the detector that identified the security vulnerability in your code. 
     */
    detectorName?: String;
    /**
     * One or more tags or categorizations that are associated with a detector. These tags are defined by type, programming language, or other classification such as maintainability or consistency.
     */
    detectorTags?: DetectorTags;
    /**
     * The identifier for the component that generated a finding such as AmazonCodeGuruSecurity.
     */
    generatorId?: String;
    /**
     * The identifier for a finding.
     */
    id?: String;
    /**
     * An object that contains the details about how to remediate a finding.
     */
    remediation?: Remediation;
    /**
     * The resource where Amazon CodeGuru Security detected a finding.
     */
    resource?: Resource;
    /**
     * The identifier for the rule that generated the finding.
     */
    ruleId?: String;
    /**
     * The severity of the finding. Severity can be critical, high, medium, low, or informational. For information on severity levels, see Finding severity in the Amazon CodeGuru Security User Guide.
     */
    severity?: Severity;
    /**
     * The status of the finding. A finding status can be open or closed. 
     */
    status?: Status;
    /**
     * The title of the finding.
     */
    title?: String;
    /**
     * The type of finding. 
     */
    type?: String;
    /**
     * The time when the finding was last updated. Findings are updated when you remediate them or when the finding code location changes. 
     */
    updatedAt?: Timestamp;
    /**
     * An object that describes the detected security vulnerability.
     */
    vulnerability?: Vulnerability;
  }
  export interface FindingIdentifier {
    /**
     * The identifier for a finding.
     */
    findingId: String;
    /**
     * The name of the scan that generated the finding. 
     */
    scanName: String;
  }
  export type FindingIdentifiers = FindingIdentifier[];
  export interface FindingMetricsValuePerSeverity {
    /**
     * A numeric value corresponding to a critical finding.
     */
    critical?: Double;
    /**
     * A numeric value corresponding to a high severity finding.
     */
    high?: Double;
    /**
     * A numeric value corresponding to an informational finding.
     */
    info?: Double;
    /**
     * A numeric value corresponding to a low severity finding.
     */
    low?: Double;
    /**
     * A numeric value corresponding to a medium severity finding.
     */
    medium?: Double;
  }
  export type Findings = Finding[];
  export type FindingsMetricList = AccountFindingsMetric[];
  export interface GetAccountConfigurationRequest {
  }
  export interface GetAccountConfigurationResponse {
    /**
     * An EncryptionConfig object that contains the KMS key ARN that is used for encryption. By default, CodeGuru Security uses an AWS-managed key for encryption. To specify your own key, call UpdateAccountConfiguration. If you do not specify a customer-managed key, returns empty.
     */
    encryptionConfig: EncryptionConfig;
  }
  export interface GetFindingsRequest {
    /**
     * The maximum number of results to return in the response. Use this parameter when paginating results. If additional results exist beyond the number you specify, the nextToken element is returned in the response. Use nextToken in a subsequent request to retrieve additional results. If not specified, returns 1000 results.
     */
    maxResults?: GetFindingsRequestMaxResultsInteger;
    /**
     * A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request. For subsequent calls, use the nextToken value returned from the previous request to continue listing results after the first page.
     */
    nextToken?: NextToken;
    /**
     * The name of the scan you want to retrieve findings from.
     */
    scanName: ScanName;
    /**
     * The status of the findings you want to get. Pass either Open, Closed, or All.
     */
    status?: Status;
  }
  export type GetFindingsRequestMaxResultsInteger = number;
  export interface GetFindingsResponse {
    /**
     * A list of findings generated by the specified scan.
     */
    findings?: Findings;
    /**
     * A pagination token. You can use this in future calls to GetFindings to continue listing results after the current page. 
     */
    nextToken?: NextToken;
  }
  export interface GetMetricsSummaryRequest {
    /**
     * The date you want to retrieve summary metrics from, rounded to the nearest day. The date must be within the past two years.
     */
    date: Timestamp;
  }
  export interface GetMetricsSummaryResponse {
    /**
     * The summary metrics from the specified date.
     */
    metricsSummary?: MetricsSummary;
  }
  export interface GetScanRequest {
    /**
     * UUID that identifies the individual scan run you want to view details about. You retrieve this when you call the CreateScan operation. Defaults to the latest scan run if missing.
     */
    runId?: Uuid;
    /**
     * The name of the scan you want to view details about.
     */
    scanName: ScanName;
  }
  export interface GetScanResponse {
    /**
     * The type of analysis CodeGuru Security performed in the scan, either Security or All. The Security type only generates findings related to security. The All type generates both security findings and quality findings.
     */
    analysisType: AnalysisType;
    /**
     * The time the scan was created.
     */
    createdAt: Timestamp;
    /**
     * Details about the error that causes a scan to fail to be retrieved.
     */
    errorMessage?: ErrorMessage;
    /**
     * The number of times a scan has been re-run on a revised resource.
     */
    numberOfRevisions?: Long;
    /**
     * UUID that identifies the individual scan run.
     */
    runId: Uuid;
    /**
     * The name of the scan.
     */
    scanName: ScanName;
    /**
     * The ARN for the scan name.
     */
    scanNameArn?: ScanNameArn;
    /**
     * The current state of the scan. Returns either InProgress, Successful, or Failed.
     */
    scanState: ScanState;
    /**
     * The time when the scan was last updated. Only available for STANDARD scan types.
     */
    updatedAt?: Timestamp;
  }
  export type HeaderKey = string;
  export type HeaderValue = string;
  export type Integer = number;
  export type KmsKeyArn = string;
  export interface ListFindingsMetricsRequest {
    /**
     * The end date of the interval which you want to retrieve metrics from. Round to the nearest day.
     */
    endDate: Timestamp;
    /**
     * The maximum number of results to return in the response. Use this parameter when paginating results. If additional results exist beyond the number you specify, the nextToken element is returned in the response. Use nextToken in a subsequent request to retrieve additional results. If not specified, returns 1000 results.
     */
    maxResults?: ListFindingsMetricsRequestMaxResultsInteger;
    /**
     * A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request. For subsequent calls, use the nextToken value returned from the previous request to continue listing results after the first page.
     */
    nextToken?: NextToken;
    /**
     * The start date of the interval which you want to retrieve metrics from. Rounds to the nearest day.
     */
    startDate: Timestamp;
  }
  export type ListFindingsMetricsRequestMaxResultsInteger = number;
  export interface ListFindingsMetricsResponse {
    /**
     * A list of AccountFindingsMetric objects retrieved from the specified time interval.
     */
    findingsMetrics?: FindingsMetricList;
    /**
     * A pagination token. You can use this in future calls to ListFindingMetrics to continue listing results after the current page. 
     */
    nextToken?: NextToken;
  }
  export interface ListScansRequest {
    /**
     * The maximum number of results to return in the response. Use this parameter when paginating results. If additional results exist beyond the number you specify, the nextToken element is returned in the response. Use nextToken in a subsequent request to retrieve additional results. If not specified, returns 100 results.
     */
    maxResults?: ListScansRequestMaxResultsInteger;
    /**
     * A token to use for paginating results that are returned in the response. Set the value of this parameter to null for the first request. For subsequent calls, use the nextToken value returned from the previous request to continue listing results after the first page.
     */
    nextToken?: NextToken;
  }
  export type ListScansRequestMaxResultsInteger = number;
  export interface ListScansResponse {
    /**
     * A pagination token. You can use this in future calls to ListScans to continue listing results after the current page.
     */
    nextToken?: NextToken;
    /**
     * A list of ScanSummary objects with information about all scans in an account.
     */
    summaries?: ScanSummaries;
  }
  export interface ListTagsForResourceRequest {
    /**
     * The ARN of the ScanName object. You can retrieve this ARN by calling CreateScan, ListScans, or GetScan.
     */
    resourceArn: ScanNameArn;
  }
  export interface ListTagsForResourceResponse {
    /**
     * An array of key-value pairs used to tag an existing scan. A tag is a custom attribute label with two parts:   A tag key. For example, CostCenter, Environment, or Secret. Tag keys are case sensitive.   An optional tag value field. For example, ************, Production, or a team name. Omitting the tag value is the same as using an empty string. Tag values are case sensitive.  
     */
    tags?: TagMap;
  }
  export type Long = number;
  export interface MetricsSummary {
    /**
     * A list of CategoryWithFindingNum objects for the top 5 finding categories with the most findings.
     */
    categoriesWithMostFindings?: CategoriesWithMostFindings;
    /**
     * The date from which the metrics summary information was retrieved.
     */
    date?: Timestamp;
    /**
     * The number of open findings of each severity.
     */
    openFindings?: FindingMetricsValuePerSeverity;
    /**
     * A list of ScanNameWithFindingNum objects for the top 3 scans with the most number of open critical findings.
     */
    scansWithMostOpenCriticalFindings?: ScansWithMostOpenCriticalFindings;
    /**
     * A list of ScanNameWithFindingNum objects for the top 3 scans with the most number of open findings.
     */
    scansWithMostOpenFindings?: ScansWithMostOpenFindings;
  }
  export type NextToken = string;
  export interface Recommendation {
    /**
     * The recommended course of action to remediate the finding.
     */
    text?: String;
    /**
     * The URL address to the recommendation for remediating the finding. 
     */
    url?: String;
  }
  export type ReferenceUrls = String[];
  export type RelatedVulnerabilities = String[];
  export interface Remediation {
    /**
     * An object that contains information about the recommended course of action to remediate a finding.
     */
    recommendation?: Recommendation;
    /**
     * A list of SuggestedFix objects. Each object contains information about a suggested code fix to remediate the finding.
     */
    suggestedFixes?: SuggestedFixes;
  }
  export type RequestHeaderMap = {[key: string]: HeaderValue};
  export interface Resource {
    /**
     * The scanName of the scan that was run on the resource.
     */
    id?: String;
    /**
     * The identifier for a section of the resource.
     */
    subResourceId?: String;
  }
  export interface ResourceId {
    /**
     * The identifier for the code file uploaded to the resource object. Returned by CreateUploadUrl when you upload resources to be scanned.
     */
    codeArtifactId?: Uuid;
  }
  export type S3Url = string;
  export type ScanName = string;
  export type ScanNameArn = string;
  export interface ScanNameWithFindingNum {
    /**
     * The number of findings generated by a scan.
     */
    findingNumber?: Integer;
    /**
     * The name of the scan.
     */
    scanName?: String;
  }
  export type ScanState = "InProgress"|"Successful"|"Failed"|string;
  export type ScanSummaries = ScanSummary[];
  export interface ScanSummary {
    /**
     *  The time when the scan was created. 
     */
    createdAt: Timestamp;
    /**
     * The identifier for the scan run. 
     */
    runId: Uuid;
    /**
     * The name of the scan. 
     */
    scanName: ScanName;
    /**
     * The ARN for the scan name.
     */
    scanNameArn?: ScanNameArn;
    /**
     * The state of the scan. A scan can be In Progress, Complete, or Failed. 
     */
    scanState: ScanState;
    /**
     * The time the scan was last updated. A scan is updated when it is re-run.
     */
    updatedAt?: Timestamp;
  }
  export type ScanType = "Standard"|"Express"|string;
  export type ScansWithMostOpenCriticalFindings = ScanNameWithFindingNum[];
  export type ScansWithMostOpenFindings = ScanNameWithFindingNum[];
  export type Severity = "Critical"|"High"|"Medium"|"Low"|"Info"|string;
  export type Status = "Closed"|"Open"|"All"|string;
  export type String = string;
  export interface SuggestedFix {
    /**
     * The suggested code fix. If applicable, includes code patch to replace your source code. 
     */
    code?: String;
    /**
     * A description of the suggested code fix and why it is being suggested. 
     */
    description?: String;
  }
  export type SuggestedFixes = SuggestedFix[];
  export type TagKey = string;
  export type TagKeyList = TagKey[];
  export type TagMap = {[key: string]: TagValue};
  export interface TagResourceRequest {
    /**
     * The ARN of the ScanName object. You can retrieve this ARN by calling CreateScan, ListScans, or GetScan.
     */
    resourceArn: ScanNameArn;
    /**
     * An array of key-value pairs used to tag an existing scan. A tag is a custom attribute label with two parts:   A tag key. For example, CostCenter, Environment, or Secret. Tag keys are case sensitive.   An optional tag value field. For example, ************, Production, or a team name. Omitting the tag value is the same as using an empty string. Tag values are case sensitive.  
     */
    tags: TagMap;
  }
  export interface TagResourceResponse {
  }
  export type TagValue = string;
  export type Timestamp = Date;
  export interface UntagResourceRequest {
    /**
     * The ARN of the ScanName object. You can retrieve this ARN by calling CreateScan, ListScans, or GetScan.
     */
    resourceArn: ScanNameArn;
    /**
     * A list of keys for each tag you want to remove from a scan.
     */
    tagKeys: TagKeyList;
  }
  export interface UntagResourceResponse {
  }
  export interface UpdateAccountConfigurationRequest {
    /**
     * The customer-managed KMS key ARN you want to use for encryption. If not specified, CodeGuru Security will use an AWS-managed key for encryption. If you previously specified a customer-managed KMS key and want CodeGuru Security to use an AWS-managed key for encryption instead, pass nothing.
     */
    encryptionConfig: EncryptionConfig;
  }
  export interface UpdateAccountConfigurationResponse {
    /**
     * An EncryptionConfig object that contains the KMS key ARN that is used for encryption. If you did not specify a customer-managed KMS key in the request, returns empty. 
     */
    encryptionConfig: EncryptionConfig;
  }
  export type Uuid = string;
  export interface Vulnerability {
    /**
     *  An object that describes the location of the detected security vulnerability in your code.
     */
    filePath?: FilePath;
    /**
     * The identifier for the vulnerability.
     */
    id?: String;
    /**
     * The number of times the vulnerability appears in your code.
     */
    itemCount?: Integer;
    /**
     * One or more URL addresses that contain details about a vulnerability.
     */
    referenceUrls?: ReferenceUrls;
    /**
     * One or more vulnerabilities that are related to the vulnerability being described.
     */
    relatedVulnerabilities?: RelatedVulnerabilities;
  }
  /**
   * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
   */
  export type apiVersion = "2018-05-10"|"latest"|string;
  export interface ClientApiVersions {
    /**
     * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
     */
    apiVersion?: apiVersion;
  }
  export type ClientConfiguration = ServiceConfigurationOptions & ClientApiVersions;
  /**
   * Contains interfaces for use with the CodeGuruSecurity client.
   */
  export import Types = CodeGuruSecurity;
}
export = CodeGuruSecurity;
