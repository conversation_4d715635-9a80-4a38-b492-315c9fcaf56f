{"cloudfront": [{"path": "lib/cloudfront/signer", "imports": [{"name": "Signer", "alias": "signer"}]}], "dynamodb": [{"path": "lib/dynamodb/document_client", "imports": [{"name": "DocumentClient", "alias": "document_client"}]}, {"path": "lib/dynamodb/converter", "imports": [{"name": "Converter", "alias": "converter"}]}], "polly": [{"path": "lib/polly/presigner", "imports": [{"name": "Presigner", "alias": "presigner"}]}], "rds": [{"path": "lib/rds/signer", "imports": [{"name": "Signer", "alias": "signer"}]}], "s3": [{"path": "lib/s3/managed_upload", "imports": [{"name": "ManagedUpload", "alias": "managed_upload"}]}, {"path": "lib/s3/presigned_post", "imports": [{"name": "PresignedPost", "alias": "presigned_post"}]}]}