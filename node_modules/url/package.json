{"name": "url", "description": "The core `url` packaged standalone for use with Browserify.", "version": "0.10.3", "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}, "main": "./url.js", "devDependencies": {"assert": "1.1.1", "mocha": "1.18.2", "zuul": "2.0.0"}, "scripts": {"test": "mocha --ui qunit test.js && zuul -- test.js", "test-local": "zuul --local -- test.js"}, "repository": {"type": "git", "url": "https://github.com/defunctzombie/node-url.git"}, "license": "MIT"}