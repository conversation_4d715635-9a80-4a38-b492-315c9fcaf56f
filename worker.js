const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');
const os = require('os');
const compressFile = require('./compressFile');
const pLimit = require('p-limit');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);

// Promisify fs functions for better async handling
const fsPromises = fs.promises;

const s3 = new AWS.S3();
const SOURCE_BUCKET = 'zmt-storage';
const TARGET_BUCKET = 'zmt-storage';
const PREFIX = 'dicom/sample/';
const TARGET_PREFIX = 'dicom/compressed/';
const CONCURRENCY = 3;
const zmtdcmPath = './zmtdcm'; // Path to the DICOM-specific compression tool

// Helper function to check if a file is a DICOM file
function isDicomFile(fileName) {
  const lowerFileName = fileName.toLowerCase();
  const dicomExtensions = ['.dcm', '.dicom', '.dic', '.ima', '.img'];
  return dicomExtensions.some(ext => lowerFileName.endsWith(ext));
}

// Helper function to compress DICOM files using zmtdcm
async function compressDicomFile(inputPath, outputDir) {
  try {
    const fileName = path.basename(inputPath);
    const nameWithoutExt = path.parse(fileName).name;
    const outputFileName = `${nameWithoutExt}_compressed.dcm`;
    const outputPath = path.join(outputDir, outputFileName);

    console.log(`Compressing DICOM file: ${inputPath} -> ${outputPath}`);

    // Ensure output directory exists
    await fsPromises.mkdir(outputDir, { recursive: true });

    // Run zmtdcm compression with correct syntax: zmtdcm input.dcm output.dcm
    const { stdout, stderr } = await exec(`${zmtdcmPath} "${inputPath}" "${outputPath}"`, {
      timeout: 5 * 60 * 1000 // 5 minutes timeout
    });

    console.log('DICOM compression stdout:', stdout);
    if (stderr) {
      console.error('DICOM compression stderr:', stderr);
    }

    // Check if the compressed file was created
    if (!fs.existsSync(outputPath)) {
      throw new Error(`Compressed DICOM file not found at expected location: ${outputPath}`);
    }

    // Verify the compressed file has content
    const compressedStats = await fsPromises.stat(outputPath);
    if (compressedStats.size === 0) {
      throw new Error('Compressed DICOM file has zero size');
    }

    console.log(`DICOM compression successful: ${fileName} (${compressedStats.size} bytes)`);
    return outputPath;
  } catch (error) {
    console.error('Error compressing DICOM file:', error);
    throw new Error(`Failed to compress DICOM file: ${error.message}`);
  }
}

/**
 * Lists objects in an S3 bucket with the given prefix
 * @param {string} bucket - S3 bucket name
 * @param {string} prefix - Prefix to filter objects
 * @returns {Promise<string[]>} - Array of object keys
 */
async function listS3Objects(bucket, prefix) {
  let isTruncated = true;
  let ContinuationToken;
  const allKeys = [];
  const processedFolders = new Set(); // Track processed folders to avoid duplicates

  try {
    console.log(`Listing objects in ${bucket} with prefix ${prefix}`);

    while (isTruncated) {
      const response = await s3.listObjectsV2({
        Bucket: bucket,
        Prefix: prefix,
        ContinuationToken,
        Delimiter: '/', // Use delimiter to get common prefixes (folders)
      }).promise();

      // Process regular files
      if (response.Contents && response.Contents.length > 0) {
        response.Contents.forEach(obj => {
          if (!obj.Key.endsWith('/')) {
            allKeys.push(obj.Key);
          }
        });
      }

      // Process folders (CommonPrefixes)
      if (response.CommonPrefixes && response.CommonPrefixes.length > 0) {
        for (const commonPrefix of response.CommonPrefixes) {
          const folderPrefix = commonPrefix.Prefix;

          // Avoid processing the same folder multiple times
          if (!processedFolders.has(folderPrefix)) {
            processedFolders.add(folderPrefix);

            // Recursively list objects in this folder
            const nestedKeys = await listS3Objects(bucket, folderPrefix);
            allKeys.push(...nestedKeys);
          }
        }
      }

      isTruncated = response.IsTruncated;
      ContinuationToken = response.NextContinuationToken;
    }

    return allKeys;
  } catch (error) {
    console.error(`Error listing objects in ${bucket} with prefix ${prefix}:`, error);
    throw error;
  }
}

/**
 * Downloads a file from S3 to a local path, maintaining folder hierarchy
 * @param {string} bucket - S3 bucket name
 * @param {string} key - S3 object key
 * @param {string} localPath - Local path to save the file
 * @returns {Promise<string>} - Local path where the file was saved
 */
async function downloadFile(bucket, key, localPath) {
  console.log(`Downloading ${key} from ${bucket} to ${localPath}`);

  try {
    // Create directory structure if it doesn't exist
    const dir = path.dirname(localPath);
    if (!fs.existsSync(dir)) {
      await fsPromises.mkdir(dir, { recursive: true });
      console.log(`Created directory structure: ${dir}`);
    }

    const params = { Bucket: bucket, Key: key };

    // Check if the key exists in S3
    try {
      await s3.headObject(params).promise();
    } catch (error) {
      if (error.code === 'NotFound') {
        throw new Error(`File not found in S3: ${bucket}/${key}`);
      }
      throw error;
    }

    // Create a write stream to the local file
    const file = fs.createWriteStream(localPath);
    const s3Stream = s3.getObject(params).createReadStream();

    return new Promise((resolve, reject) => {
      s3Stream.pipe(file)
        .on('error', (err) => {
          console.error(`Error downloading file: ${err.message}`);
          // Close the file stream to prevent file handle leaks
          file.end();
          reject(err);
        })
        .on('close', () => {
          console.log(`Download complete: ${localPath}`);
          resolve(localPath);
        });
    });
  } catch (error) {
    console.error(`Error in downloadFile for ${key}: ${error.message}`);
    throw error;
  }
}

/**
 * Uploads a file to S3
 * @param {string} bucket - S3 bucket name
 * @param {string} key - S3 object key
 * @param {string} localPath - Local path of the file to upload
 * @returns {Promise<void>}
 */
async function uploadFile(bucket, key, localPath) {
  try {
    console.log(`Uploading ${localPath} to ${bucket}/${key}`);

    // Ensure the file exists before attempting to upload
    if (!fs.existsSync(localPath)) {
      throw new Error(`File not found: ${localPath}`);
    }

    // Read file content
    const fileContent = await fsPromises.readFile(localPath);

    // Ensure the target key's directory structure exists in S3
    // This is a no-op in S3 since it automatically creates the necessary "folders"
    // but we log it for clarity
    const dirPath = path.dirname(key);
    if (dirPath !== '.') {
      console.log(`Ensuring directory structure exists in S3: ${bucket}/${dirPath}/`);
    }

    // Upload to S3
    await s3.putObject({
      Bucket: bucket,
      Key: key,
      Body: fileContent,
    }).promise();

    console.log(`Upload complete: ${bucket}/${key}`);
  } catch (error) {
    console.error(`Error uploading file ${localPath} to ${bucket}/${key}:`, error);
    throw error;
  }
}

/**
 * Processes a single file: download, compress, upload, cleanup
 * @param {string} key - S3 object key to process
 * @returns {Promise<void>}
 */
async function processFile(key) {
  try {
    console.log(`Processing: ${key}`);

    // Create a local directory structure that mirrors the S3 key structure
    const dirPath = path.dirname(key);
    const localDir = path.join(os.tmpdir(), dirPath);

    // Ensure the local directory exists
    if (!fs.existsSync(localDir)) {
      await fsPromises.mkdir(localDir, { recursive: true });
      console.log(`Created local directory: ${localDir}`);
    }

    // Create the full local path for the downloaded file
    const localInput = path.join(os.tmpdir(), key);

    // Download the file from S3
    await downloadFile(SOURCE_BUCKET, key, localInput);

    // Check if the downloaded item is a directory or a file
    const stats = await fsPromises.stat(localInput);
    let compressionResult;
    let compressedFilePath;
    let originalKey = key;

    if (stats.isDirectory()) {
      console.log(`${localInput} is a directory, compressing entire directory`);
      // If it's a directory, compress the entire directory
      compressionResult = await compressFile(localInput, key);
      compressedFilePath = compressionResult.compressedFilePath;
      originalKey = compressionResult.originalKey;
    } else {
      // Check if it's a DICOM file and use DICOM compression
      const fileName = path.basename(localInput);
      if (isDicomFile(fileName)) {
        console.log(`${fileName} is a DICOM file, using DICOM compression`);
        // Use DICOM-specific compression
        const outputDir = path.dirname(localInput);
        compressedFilePath = await compressDicomFile(localInput, outputDir);
      } else {
        // Use regular compression for non-DICOM files
        compressionResult = await compressFile(localInput, key);
        compressedFilePath = compressionResult.compressedFilePath;
        originalKey = compressionResult.originalKey;
      }
    }

    console.log(`File compressed successfully: ${compressedFilePath}`);

    // Upload the compressed file to S3 with the same directory structure
    // Use the original key to maintain the exact folder hierarchy

    // Create the target key with the exact same folder structure
    let targetKey;

    // Determine the file extension based on compression type
    const originalFileName = path.basename(originalKey);
    let targetExtension;

    if (isDicomFile(originalFileName)) {
      // For DICOM files, keep the .dcm extension since zmtdcm outputs .dcm files
      targetExtension = '.dcm';
    } else {
      // For other files, use .zmt extension
      targetExtension = '.zmt';
    }

    // Parse the path to extract the parts after "sample/"
    const pathParts = originalKey.split('/');
    const sampleIndex = pathParts.indexOf('sample');

    if (sampleIndex !== -1) {
      // Get everything after "sample/" to maintain the exact hierarchy
      const pathAfterSample = pathParts.slice(sampleIndex + 1).join('/');

      if (pathAfterSample) {
        // There are subdirectories after "sample/"
        const dirPath = path.dirname(pathAfterSample);
        const fileName = path.basename(pathAfterSample);

        if (dirPath === '.') {
          // File is directly in the sample folder
          targetKey = `${TARGET_PREFIX}sample/${fileName.replace(/\.[^/.]+$/, targetExtension)}`;
        } else {
          // File is in a subdirectory of sample
          targetKey = `${TARGET_PREFIX}sample/${dirPath}/${fileName.replace(/\.[^/.]+$/, targetExtension)}`;
        }
      } else {
        // File is directly in the sample folder
        targetKey = `${TARGET_PREFIX}sample/${path.basename(originalKey).replace(/\.[^/.]+$/, targetExtension)}`;
      }
    } else {
      // Fallback if "sample" is not in the path
      const dirPath = path.dirname(originalKey);
      targetKey = `${TARGET_PREFIX}${dirPath}/${path.basename(originalKey).replace(/\.[^/.]+$/, targetExtension)}`;
    }

    await uploadFile(TARGET_BUCKET, targetKey, compressedFilePath);
    console.log(`Compressed file uploaded to S3: ${TARGET_BUCKET}/${targetKey}`);

    // Clean up the input file
    if (stats.isDirectory()) {
      // If it's a directory, recursively remove it
      await removeDirectory(localInput);
      console.log(`Cleaned up input directory: ${localInput}`);
    } else {
      // If it's a file, just remove the file
      await fsPromises.unlink(localInput);
      console.log(`Cleaned up input file: ${localInput}`);
    }

    // Clean up the compressed file from archives
    await fsPromises.unlink(compressedFilePath);
    console.log(`Cleaned up compressed file: ${compressedFilePath}`);

    console.log(`✅ Finished processing: ${key}`);
  } catch (err) {
    console.error(`❌ Error processing ${key}:`, err);

    // Clean up any files that might have been created, even on error
    try {
      const localInput = path.join(os.tmpdir(), key);

      if (fs.existsSync(localInput)) {
        const stats = await fsPromises.stat(localInput);

        if (stats.isDirectory()) {
          // If it's a directory, recursively remove it
          await removeDirectory(localInput);
          console.log(`Cleaned up input directory after error: ${localInput}`);
        } else {
          // If it's a file, just remove the file
          await fsPromises.unlink(localInput);
          console.log(`Cleaned up input file after error: ${localInput}`);
        }
      }

      // Try to find and clean up any compressed files that might have been created
      const archivesBaseDir = path.resolve(__dirname, 'archives');
      if (fs.existsSync(archivesBaseDir)) {
        const filename = path.basename(key);
        const dirPath = path.dirname(key);

        // Handle nested directories properly - use the exact same path structure
        // This ensures we clean up files in the exact same folder hierarchy
        const compressedDir = path.join(archivesBaseDir, dirPath);
        const compressedPath = path.join(compressedDir, `${filename}.zmt`);

        try {
          if (fs.existsSync(compressedPath)) {
            await fsPromises.unlink(compressedPath);
            console.log(`Cleaned up compressed file after error: ${compressedPath}`);
          }

          // Check if the compressed directory is now empty and can be removed
          if (fs.existsSync(compressedDir)) {
            // Recursively check and remove empty directories
            await cleanupEmptyDirectories(compressedDir);
          }
        } catch (unlinkErr) {
          console.error(`Error cleaning up compressed file or directory: ${unlinkErr.message}`);
        }
      }
    } catch (cleanupErr) {
      console.error('Error during cleanup:', cleanupErr);
    }
  }
}

/**
 * Recursively checks and removes empty directories
 * @param {string} dirPath - Path to the directory to check
 * @returns {Promise<boolean>} - True if the directory was empty and removed, false otherwise
 */
async function cleanupEmptyDirectories(dirPath) {
  try {
    // Check if directory exists
    if (!fs.existsSync(dirPath)) {
      return false;
    }

    // Get all entries in the directory
    const entries = await fsPromises.readdir(dirPath);

    // Process each entry
    for (const entry of entries) {
      const entryPath = path.join(dirPath, entry);
      const stats = await fsPromises.stat(entryPath);

      // If it's a directory, recursively check if it's empty
      if (stats.isDirectory()) {
        // If the subdirectory was not empty, this directory is not empty
        const wasEmpty = await cleanupEmptyDirectories(entryPath);
        if (!wasEmpty) {
          return false;
        }
      } else {
        // If there's a file, the directory is not empty
        return false;
      }
    }

    // If we get here, the directory is empty or all subdirectories were empty and removed
    // Check again to make sure it's still empty
    const remainingEntries = await fsPromises.readdir(dirPath);
    if (remainingEntries.length === 0) {
      await fsPromises.rmdir(dirPath);
      console.log(`Removed empty directory: ${dirPath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error checking/removing empty directory ${dirPath}:`, error);
    return false;
  }
}

/**
 * Recursively removes a directory and all its contents
 * @param {string} dirPath - Path to the directory to remove
 * @returns {Promise<void>}
 */
async function removeDirectory(dirPath) {
  try {
    const entries = await fsPromises.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory()) {
        // Recursively remove subdirectories
        await removeDirectory(fullPath);
      } else {
        // Remove files
        await fsPromises.unlink(fullPath);
      }
    }

    // Remove the empty directory
    await fsPromises.rmdir(dirPath);
  } catch (error) {
    console.error(`Error removing directory ${dirPath}:`, error);
    throw error;
  }
}

/**
 * Cleans up old files in the archives directory
 * @returns {Promise<void>}
 */
async function cleanupArchivesDirectory() {
  try {
    const archivesDir = path.resolve(__dirname, 'archives');

    // Check if directory exists
    if (!fs.existsSync(archivesDir)) {
      console.log('Archives directory does not exist, creating it');
      await fsPromises.mkdir(archivesDir, { recursive: true });
      return;
    }

    // Get current time
    const now = Date.now();

    // Recursively clean up files older than 1 hour (3600000 ms)
    await cleanupDirectory(archivesDir, now);
  } catch (error) {
    console.error('Error cleaning up archives directory:', error);
  }
}

/**
 * Recursively cleans up files in a directory that are older than 1 hour
 * @param {string} directory - Directory to clean up
 * @param {number} now - Current timestamp
 * @returns {Promise<void>}
 */
async function cleanupDirectory(directory, now) {
  try {
    // Check if directory exists before attempting to read it
    if (!fs.existsSync(directory)) {
      console.log(`Directory does not exist, skipping cleanup: ${directory}`);
      return;
    }

    // Get all files and directories in the current directory
    const entries = await fsPromises.readdir(directory, { withFileTypes: true });

    // Process each entry in the directory
    for (const entry of entries) {
      const entryPath = path.join(directory, entry.name);

      try {
        if (entry.isDirectory()) {
          // Recursively clean up subdirectories
          await cleanupDirectory(entryPath, now);

          // Check if directory is empty after cleaning up files
          if (fs.existsSync(entryPath)) { // Check if directory still exists
            const remainingEntries = await fsPromises.readdir(entryPath);
            if (remainingEntries.length === 0) {
              await fsPromises.rmdir(entryPath);
              console.log(`Removed empty directory: ${entryPath}`);
            }
          }
        } else {
          // Check file age and delete if older than 1 hour
          const stats = await fsPromises.stat(entryPath);
          const fileAge = now - stats.mtime.getTime();

          if (fileAge > 3600000) {
            await fsPromises.unlink(entryPath);
            console.log(`Cleaned up old file: ${entryPath}`);
          }
        }
      } catch (entryError) {
        // Log error but continue processing other entries
        console.error(`Error processing entry ${entryPath}:`, entryError);
      }
    }
  } catch (error) {
    console.error(`Error cleaning up directory ${directory}:`, error);
  }
}

/**
 * Main function to orchestrate the entire process
 * @returns {Promise<void>}
 */
async function main() {
  // Clean up archives directory before starting
  await cleanupArchivesDirectory();

  // Set up periodic cleanup every hour
  setInterval(cleanupArchivesDirectory, 3600000);

  // List all objects to process
  const keys = await listS3Objects(SOURCE_BUCKET, PREFIX);
  console.log(`Found ${keys.length} files to process`);

  // Process files with concurrency limit
  const limit = pLimit(CONCURRENCY);
  const tasks = keys.map(key => limit(() => processFile(key)));

  // Wait for all tasks to complete
  await Promise.all(tasks);
  console.log('All files processed successfully');
}

// Start the process
main().catch(error => {
  console.error('Fatal error in main process:', error);
  process.exit(1);
});
