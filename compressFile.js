const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// Promisify for better async handling
const fsPromises = fs.promises;

/**
 * Compresses a file or directory using the archiver package, maintaining folder hierarchy
 * @param {string} localInputPath - Path to the file or directory to compress
 * @param {string} originalKey - Original S3 key to maintain folder hierarchy
 * @returns {Promise<{compressedFilePath: string, originalKey: string}>} - Path to the compressed file and original key
 */
async function compressFile(localInputPath, originalKey) {
  try {
    // Extract the filename and directory structure from the original key
    const filename = path.basename(originalKey);
    const dirPath = path.dirname(originalKey);

    // Create archives directory with the same folder structure as the original key
    const archivesBaseDir = path.resolve(__dirname, 'archives');
    const archivesDir = path.join(archivesBaseDir, dirPath);
    console.log(`archivesDir: ${archivesDir}`);

    // Ensure archives base directory exists
    if (!fs.existsSync(archivesBaseDir)) {
      await fsPromises.mkdir(archivesBaseDir, { recursive: true });
      console.log(`Created archives base directory: ${archivesBaseDir}`);
    }

    // Ensure the full directory path exists
    if (!fs.existsSync(archivesDir)) {
      await fsPromises.mkdir(archivesDir, { recursive: true });
      console.log(`Created directory structure: ${archivesDir}`);
    }

    // Create output file path with .zmt extension in the same directory structure
    // Ensure the filename matches the original filename but with .zmt extension
    const outputPath = path.join(archivesDir, `${filename}.zmt`);
    console.log(`Compressing file: ${localInputPath} to ${outputPath}`);

    // Create a file to stream archive data to
    const output = fs.createWriteStream(outputPath);
    const archive = archiver('zip', {
      zlib: { level: 9 } // Set the compression level (0-9)
    });

    // Set up warning and error handlers
    archive.on('warning', (err) => {
      if (err.code === 'ENOENT') {
        console.warn('Archive warning:', err);
      } else {
        throw err;
      }
    });

    // Listen for all archive data to be written
    const archiveFinished = new Promise((resolve, reject) => {
      output.on('close', () => {
        console.log(`Archive created: ${outputPath}, total bytes: ${archive.pointer()}`);
        resolve();
      });

      archive.on('error', (err) => {
        console.error('Archive error:', err);
        reject(err);
      });
    });

    // Pipe archive data to the file
    archive.pipe(output);

    // Add the file to the archive
    try {
      const fileStats = await fsPromises.stat(localInputPath);

      if (fileStats.isDirectory()) {
        // If it's a directory, add the entire directory with the original path structure
        // We want to preserve the full path structure within the archive
        // Get the base directory name from the original key path
        const baseDir = path.basename(dirPath);

        // Use the directory name as the base in the archive
        // This preserves the nested structure while keeping the archive organized
        archive.directory(localInputPath, baseDir);
        console.log(`Adding directory: ${localInputPath} as ${baseDir}`);
      } else {
        // If it's a file, add the file with its original path structure
        // The name parameter specifies the path within the archive
        // For files, we just use the filename without any path
        archive.file(localInputPath, { name: filename });
        console.log(`Adding file: ${localInputPath} as ${filename}`);
      }

      // Finalize the archive (this is also a promise)
      await archive.finalize();

      // Wait for the archive to be fully written
      await archiveFinished;

      // Verify the archive was created and has content
      const archiveStats = await fsPromises.stat(outputPath);
      if (archiveStats.size === 0) {
        throw new Error('Created archive has zero size. Archiving may not be complete.');
      }

      // Return both the compressed file path and the original key to maintain the exact folder structure
      return {
        compressedFilePath: outputPath,
        originalKey: originalKey
      };
    } catch (err) {
      console.error(`Error adding files to archive: ${err.message}`);
      throw err;
    }
  } catch (error) {
    console.error(`Compression error for ${localInputPath}:`, error);
    throw error;
  }
}

module.exports = compressFile;
